<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Manager\ManagerDashboardController;
use App\Http\Controllers\FieldOfficer\FieldOfficerDashboardController;
use App\Http\Controllers\Member\MemberDashboardController;

// Public Routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication Routes
Auth::routes();

// Authenticated Routes
Route::middleware(['auth'])->group(function () {
    // General Dashboard (redirects to role-specific dashboard)
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Admin Routes
    Route::middleware(['role:admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

        // Branch Management
        Route::resource('branches', \App\Http\Controllers\Admin\BranchController::class);
        Route::get('/branches/{branch}/statistics', [\App\Http\Controllers\Admin\BranchController::class, 'statistics'])->name('branches.statistics');
        Route::post('/branches/{branch}/assign-manager', [\App\Http\Controllers\Admin\BranchController::class, 'assignManager'])->name('branches.assign-manager');

        // User Management
        Route::resource('users', \App\Http\Controllers\Admin\UserController::class);
        Route::post('/users/bulk-action', [\App\Http\Controllers\Admin\UserController::class, 'bulkAction'])->name('users.bulk-action');
        Route::post('/users/{user}/toggle-status', [\App\Http\Controllers\Admin\UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::post('/users/{user}/reset-password', [\App\Http\Controllers\Admin\UserController::class, 'resetPassword'])->name('users.reset-password');

        // Member Management
        Route::resource('members', \App\Http\Controllers\Admin\MemberController::class);
        Route::get('/members/{member}/loans', [\App\Http\Controllers\Admin\MemberController::class, 'loans'])->name('members.loans');
        Route::get('/members/{member}/savings', [\App\Http\Controllers\Admin\MemberController::class, 'savings'])->name('members.savings');
        Route::get('/members/export/pdf', [\App\Http\Controllers\Admin\MemberController::class, 'exportPdf'])->name('members.export.pdf');

        // Loan Management
        Route::prefix('loans')->name('loans.')->group(function () {
            Route::get('/pending', [\App\Http\Controllers\Admin\LoanController::class, 'pending'])->name('pending');
            Route::get('/active', [\App\Http\Controllers\Admin\LoanController::class, 'active'])->name('active');
            Route::get('/overdue', [\App\Http\Controllers\Admin\LoanController::class, 'overdue'])->name('overdue');
            Route::post('/applications/{application}/approve', [\App\Http\Controllers\Admin\LoanController::class, 'approve'])->name('approve');
            Route::post('/applications/{application}/reject', [\App\Http\Controllers\Admin\LoanController::class, 'reject'])->name('reject');
            Route::get('/calculator', [\App\Http\Controllers\Admin\LoanController::class, 'calculator'])->name('calculator');
        });

        // Advertisement Management
        Route::resource('advertisements', \App\Http\Controllers\Admin\AdvertisementController::class);
        Route::post('/advertisements/{advertisement}/toggle-status', [\App\Http\Controllers\Admin\AdvertisementController::class, 'toggleStatus'])->name('advertisements.toggle-status');
        Route::get('/advertisements/{advertisement}/preview', [\App\Http\Controllers\Admin\AdvertisementController::class, 'preview'])->name('advertisements.preview');

        // Analytics & Reports
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\AnalyticsController::class, 'index'])->name('index');
            Route::get('/installment-trends', [\App\Http\Controllers\Admin\AnalyticsController::class, 'installmentTrends'])->name('installment-trends');
            Route::get('/default-rates', [\App\Http\Controllers\Admin\AnalyticsController::class, 'defaultRates'])->name('default-rates');
            Route::get('/branch-comparison', [\App\Http\Controllers\Admin\AnalyticsController::class, 'branchComparison'])->name('branch-comparison');
            Route::get('/financial-summary', [\App\Http\Controllers\Admin\AnalyticsController::class, 'financialSummary'])->name('financial-summary');
            Route::get('/export/{type}', [\App\Http\Controllers\Admin\AnalyticsController::class, 'export'])->name('export');
        });

        // Activities Log
        Route::get('/activities', [\App\Http\Controllers\Admin\ActivityController::class, 'index'])->name('activities');
    });

    // Manager Routes
    Route::middleware(['role:manager', 'branch.access'])->prefix('manager')->name('manager.')->group(function () {
        Route::get('/dashboard', [ManagerDashboardController::class, 'index'])->name('dashboard');

        // Loan Application Management
        Route::resource('loan-applications', \App\Http\Controllers\Manager\LoanApplicationController::class)->only(['index', 'show']);
        Route::post('/loan-applications/{loanApplication}/approve', [\App\Http\Controllers\Manager\LoanApplicationController::class, 'approve'])->name('loan-applications.approve');
        Route::post('/loan-applications/{loanApplication}/reject', [\App\Http\Controllers\Manager\LoanApplicationController::class, 'reject'])->name('loan-applications.reject');
        Route::get('/loan-calculator', [\App\Http\Controllers\Manager\LoanApplicationController::class, 'calculator'])->name('loan-calculator');
        Route::post('/calculate-repayment', [\App\Http\Controllers\Manager\LoanApplicationController::class, 'calculateRepayment'])->name('calculate-repayment');
        Route::get('/pending-applications-count', [\App\Http\Controllers\Manager\LoanApplicationController::class, 'getPendingCount'])->name('pending-applications-count');

        // Branch Financial Management
        Route::resource('transactions', \App\Http\Controllers\Manager\BranchTransactionController::class);
        Route::post('/transactions/report', [\App\Http\Controllers\Manager\BranchTransactionController::class, 'report'])->name('transactions.report');
        Route::get('/monthly-summary', [\App\Http\Controllers\Manager\BranchTransactionController::class, 'getMonthlySummary'])->name('monthly-summary');

        // Field Officer Management
        Route::resource('field-officers', \App\Http\Controllers\Manager\FieldOfficerController::class)->only(['index', 'show']);
        Route::get('/field-officers-comparison', [\App\Http\Controllers\Manager\FieldOfficerController::class, 'comparison'])->name('field-officers.comparison');
        Route::match(['get', 'post'], '/assign-targets', [\App\Http\Controllers\Manager\FieldOfficerController::class, 'assignTargets'])->name('assign-targets');
        Route::get('/field-officers/{fieldOfficer}/performance', [\App\Http\Controllers\Manager\FieldOfficerController::class, 'getPerformanceData'])->name('field-officers.performance');

        // Member Management
        Route::resource('members', \App\Http\Controllers\Manager\MemberController::class)->only(['index', 'show']);
        Route::get('/members/{member}/loans', [\App\Http\Controllers\Manager\MemberController::class, 'loans'])->name('members.loans');
        Route::get('/members/{member}/savings', [\App\Http\Controllers\Manager\MemberController::class, 'savings'])->name('members.savings');
        Route::get('/members-analytics', [\App\Http\Controllers\Manager\MemberController::class, 'analytics'])->name('members.analytics');
        Route::post('/members/export', [\App\Http\Controllers\Manager\MemberController::class, 'export'])->name('members.export');
        Route::get('/members-statistics', [\App\Http\Controllers\Manager\MemberController::class, 'getStatistics'])->name('members.statistics');

        // Collection Monitoring
        Route::get('/collections', [\App\Http\Controllers\Manager\CollectionController::class, 'index'])->name('collections.index');
        Route::get('/collections/overdue', [\App\Http\Controllers\Manager\CollectionController::class, 'overdue'])->name('collections.overdue');
        Route::get('/collections/reports', [\App\Http\Controllers\Manager\CollectionController::class, 'reports'])->name('collections.reports');
        Route::post('/collections/export-report', [\App\Http\Controllers\Manager\CollectionController::class, 'exportReport'])->name('collections.export-report');
        Route::get('/collections/statistics', [\App\Http\Controllers\Manager\CollectionController::class, 'getStatistics'])->name('collections.statistics');
    });

    // Field Officer Routes
    Route::middleware(['role:field_officer', 'branch.access'])->prefix('field-officer')->name('field-officer.')->group(function () {
        Route::get('/dashboard', [FieldOfficerDashboardController::class, 'index'])->name('dashboard');

        // Member Management
        Route::resource('members', \App\Http\Controllers\FieldOfficer\MemberController::class);
        Route::get('/members/{member}/details', [\App\Http\Controllers\FieldOfficer\MemberController::class, 'getMemberDetails'])->name('members.details');
        Route::get('/search-members', [\App\Http\Controllers\FieldOfficer\MemberController::class, 'search'])->name('members.search');

        // Loan Applications
        Route::resource('loan-applications', \App\Http\Controllers\FieldOfficer\LoanApplicationController::class);
        Route::post('/calculate-repayment', [\App\Http\Controllers\FieldOfficer\LoanApplicationController::class, 'calculateRepayment'])->name('calculate-repayment');
        Route::get('/members/{member}/eligibility', [\App\Http\Controllers\FieldOfficer\LoanApplicationController::class, 'checkEligibility'])->name('members.eligibility');

        // Collections
        Route::get('/collections', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'index'])->name('collections.index');
        Route::get('/collections/collect', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'collect'])->name('collections.collect');
        Route::post('/collections', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'store'])->name('collections.store');
        Route::get('/collections/{installment}/receipt', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'receipt'])->name('collections.receipt');
        Route::get('/collections/history', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'history'])->name('collections.history');
        Route::get('/collections/daily-summary', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'dailySummary'])->name('collections.daily-summary');
        Route::get('/members/{member}/collection-details', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'getMemberCollectionDetails'])->name('members.collection-details');
        Route::post('/calculate-late-fee', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'calculateLateFee'])->name('calculate-late-fee');
        Route::get('/search-collection-members', [\App\Http\Controllers\FieldOfficer\CollectionController::class, 'searchMembers'])->name('collections.search-members');

        // Loan Calculator
        Route::get('/loan-calculator', [\App\Http\Controllers\FieldOfficer\LoanCalculatorController::class, 'index'])->name('loan-calculator.index');
        Route::post('/loan-calculator/calculate', [\App\Http\Controllers\FieldOfficer\LoanCalculatorController::class, 'calculate'])->name('loan-calculator.calculate');
        Route::post('/loan-calculator/compare', [\App\Http\Controllers\FieldOfficer\LoanCalculatorController::class, 'compare'])->name('loan-calculator.compare');
        Route::get('/loan-calculator/presets', [\App\Http\Controllers\FieldOfficer\LoanCalculatorController::class, 'getPresets'])->name('loan-calculator.presets');
        Route::post('/loan-calculator/max-loan', [\App\Http\Controllers\FieldOfficer\LoanCalculatorController::class, 'calculateMaxLoan'])->name('loan-calculator.max-loan');
    });

    // Member Routes
    Route::middleware(['role:member'])->prefix('member')->name('member.')->group(function () {
        Route::get('/dashboard', [MemberDashboardController::class, 'index'])->name('dashboard');

        // Loan Management Routes
        Route::prefix('loans')->name('loans.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Member\MemberLoanController::class, 'index'])->name('index');
            Route::get('/{loan}', [\App\Http\Controllers\Member\MemberLoanController::class, 'show'])->name('show');
            Route::get('/{loan}/schedule', [\App\Http\Controllers\Member\MemberLoanController::class, 'schedule'])->name('schedule');
            Route::get('/{loan}/download-statement', [\App\Http\Controllers\Member\MemberLoanController::class, 'downloadStatement'])->name('download-statement');
            Route::get('/applications/list', [\App\Http\Controllers\Member\MemberLoanController::class, 'applications'])->name('applications');
            Route::get('/applications/{application}', [\App\Http\Controllers\Member\MemberLoanController::class, 'applicationShow'])->name('application-show');
        });

        // Savings Management Routes
        Route::prefix('savings')->name('savings.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Member\MemberSavingsController::class, 'index'])->name('index');
            Route::get('/{savingAccount}', [\App\Http\Controllers\Member\MemberSavingsController::class, 'show'])->name('show');
            Route::get('/{savingAccount}/transactions', [\App\Http\Controllers\Member\MemberSavingsController::class, 'transactions'])->name('transactions');
            Route::get('/{savingAccount}/download-statement', [\App\Http\Controllers\Member\MemberSavingsController::class, 'downloadStatement'])->name('download-statement');
            Route::get('/goals/overview', [\App\Http\Controllers\Member\MemberSavingsController::class, 'goals'])->name('goals');
        });

        // Installment Management Routes
        Route::prefix('installments')->name('installments.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Member\MemberInstallmentController::class, 'index'])->name('index');
            Route::get('/{installment}', [\App\Http\Controllers\Member\MemberInstallmentController::class, 'show'])->name('show');
            Route::get('/schedule/view', [\App\Http\Controllers\Member\MemberInstallmentController::class, 'schedule'])->name('schedule');
            Route::get('/history/view', [\App\Http\Controllers\Member\MemberInstallmentController::class, 'history'])->name('history');
            Route::get('/{installment}/download-receipt', [\App\Http\Controllers\Member\MemberInstallmentController::class, 'downloadReceipt'])->name('download-receipt');
        });

        // Profile Management Routes
        Route::prefix('profile')->name('profile.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Member\MemberProfileController::class, 'index'])->name('index');
            Route::get('/edit', [\App\Http\Controllers\Member\MemberProfileController::class, 'edit'])->name('edit');
            Route::put('/update', [\App\Http\Controllers\Member\MemberProfileController::class, 'update'])->name('update');
            Route::get('/change-password', [\App\Http\Controllers\Member\MemberProfileController::class, 'changePassword'])->name('change-password');
            Route::put('/update-password', [\App\Http\Controllers\Member\MemberProfileController::class, 'updatePassword'])->name('update-password');
            Route::get('/emergency-contacts', [\App\Http\Controllers\Member\MemberProfileController::class, 'emergencyContacts'])->name('emergency-contacts');
            Route::get('/membership-details', [\App\Http\Controllers\Member\MemberProfileController::class, 'membershipDetails'])->name('membership-details');
            Route::get('/documents', [\App\Http\Controllers\Member\MemberProfileController::class, 'documents'])->name('documents');
            Route::get('/download-photo', [\App\Http\Controllers\Member\MemberProfileController::class, 'downloadPhoto'])->name('download-photo');
        });
    });
});

// Legacy route redirect
Route::get('/home', function () {
    return redirect()->route('dashboard');
})->middleware('auth');
