<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Member extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id',
        'name',
        'father_or_husband_name',
        'mother_name',
        'present_address',
        'permanent_address',
        'nid_number',
        'date_of_birth',
        'religion',
        'phone_number',
        'blood_group',
        'photo',
        'occupation',
        'reference_id',
        'branch_id',
        'created_by',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
    ];

    /**
     * Relationships
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function reference()
    {
        return $this->belongsTo(Member::class, 'reference_id');
    }

    public function referencedMembers()
    {
        return $this->hasMany(Member::class, 'reference_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'member_id', 'member_id');
    }

    public function loanApplications()
    {
        return $this->hasMany(LoanApplication::class);
    }

    public function loans()
    {
        return $this->hasMany(Loan::class);
    }

    public function savingAccounts()
    {
        return $this->hasMany(SavingAccount::class);
    }

    /**
     * Business logic methods
     */
    public function getTotalLoans(): int
    {
        return $this->loans()->count();
    }

    public function getCurrentLoanStatus(): string
    {
        $activeLoan = $this->loans()
            ->whereHas('installments', function ($query) {
                $query->where('status', 'pending');
            })
            ->first();

        if (!$activeLoan) {
            return 'No Active Loan';
        }

        $overdueInstallments = $activeLoan->installments()
            ->where('status', 'pending')
            ->where('installment_date', '<', now())
            ->count();

        return $overdueInstallments > 0 ? 'Overdue' : 'Current';
    }

    public function getSavingsBalance(): float
    {
        return $this->savingAccounts()->sum('current_balance');
    }

    public function getTotalLoanAmount(): float
    {
        return $this->loans()->sum('loan_amount');
    }

    public function getTotalPaidAmount(): float
    {
        return $this->loans()
            ->with('installments')
            ->get()
            ->sum(function ($loan) {
                return $loan->installments->where('status', 'paid')->sum('amount');
            });
    }

    public function getTotalOutstandingAmount(): float
    {
        return $this->loans()
            ->with('installments')
            ->get()
            ->sum(function ($loan) {
                return $loan->installments->where('status', 'pending')->sum('amount');
            });
    }

    public function getNextLoanCycleNumber(): int
    {
        return $this->loanApplications()->max('loan_cycle_number') + 1;
    }

    public function canApplyForLoan(): bool
    {
        // Check if member has any pending loan applications
        $pendingApplications = $this->loanApplications()
            ->where('status', 'pending')
            ->count();

        // Check if member has any active loans with pending installments
        $activeLoans = $this->loans()
            ->whereHas('installments', function ($query) {
                $query->where('status', 'pending');
            })
            ->count();

        return $pendingApplications === 0 && $activeLoans === 0;
    }

    /**
     * Scopes
     */
    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeWithActiveLoans($query)
    {
        return $query->whereHas('loans', function ($q) {
            $q->whereHas('installments', function ($installmentQuery) {
                $installmentQuery->where('status', 'pending');
            });
        });
    }

    public function scopeWithOverdueLoans($query)
    {
        return $query->whereHas('loans', function ($q) {
            $q->whereHas('installments', function ($installmentQuery) {
                $installmentQuery->where('status', 'pending')
                    ->where('installment_date', '<', now());
            });
        });
    }

    /**
     * Accessors
     */
    public function getAgeAttribute(): int
    {
        return $this->date_of_birth ? $this->date_of_birth->age : 0;
    }

    public function getFormattedPhoneAttribute(): string
    {
        return $this->phone_number ? '+880' . ltrim($this->phone_number, '0') : '';
    }
}
