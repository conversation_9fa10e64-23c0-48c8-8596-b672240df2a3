<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoanApplication extends Model
{
    use HasFactory;

    protected $fillable = [
        'member_id',
        'applied_amount',
        'reason',
        'loan_cycle_number',
        'recommender',
        'status',
        'advance_payment',
        'reviewed_by',
        'reviewed_at',
        'applied_at',
    ];

    protected $casts = [
        'applied_amount' => 'decimal:2',
        'advance_payment' => 'decimal:2',
        'reviewed_at' => 'datetime',
        'applied_at' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function member()
    {
        return $this->belongsTo(Member::class);
    }

    public function reviewedBy()
    {
        return $this->belongsTo(User::class, 'reviewed_by');
    }

    public function loan()
    {
        return $this->hasOne(Loan::class);
    }

    /**
     * Business logic methods
     */
    public function approve(User $reviewer, array $loanDetails = []): Loan
    {
        $this->update([
            'status' => 'approved',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
        ]);

        // Create loan record
        $loan = $this->loan()->create([
            'loan_application_id' => $this->id,
            'loan_amount' => $loanDetails['loan_amount'] ?? $this->applied_amount,
            'interest_rate' => $loanDetails['interest_rate'] ?? 12.0,
            'loan_duration_months' => $loanDetails['loan_duration_months'] ?? 12,
            'repayment_method' => $loanDetails['repayment_method'] ?? 'weekly',
            'loan_date' => $loanDetails['loan_date'] ?? now(),
            'advance_payment' => $this->advance_payment,
            'first_installment_date' => $loanDetails['first_installment_date'] ?? now()->addWeek(),
            'last_installment_date' => $loanDetails['last_installment_date'] ?? now()->addMonths($loanDetails['loan_duration_months'] ?? 12),
        ]);

        // Generate installments
        $loan->generateInstallments();

        return $loan;
    }

    public function reject(User $reviewer, string $reason = null): void
    {
        $this->update([
            'status' => 'rejected',
            'reviewed_by' => $reviewer->id,
            'reviewed_at' => now(),
            'rejection_reason' => $reason,
        ]);
    }

    public function calculateRepaymentAmount(float $interestRate = 12.0, int $durationMonths = 12): float
    {
        $principal = $this->applied_amount - $this->advance_payment;
        $monthlyInterestRate = $interestRate / 100 / 12;
        
        if ($monthlyInterestRate == 0) {
            return $principal / $durationMonths;
        }

        $monthlyPayment = $principal * 
            ($monthlyInterestRate * pow(1 + $monthlyInterestRate, $durationMonths)) /
            (pow(1 + $monthlyInterestRate, $durationMonths) - 1);

        return round($monthlyPayment, 2);
    }

    public function calculateTotalRepaymentAmount(float $interestRate = 12.0, int $durationMonths = 12): float
    {
        $monthlyPayment = $this->calculateRepaymentAmount($interestRate, $durationMonths);
        return $monthlyPayment * $durationMonths + $this->advance_payment;
    }

    public function calculateTotalInterest(float $interestRate = 12.0, int $durationMonths = 12): float
    {
        $totalRepayment = $this->calculateTotalRepaymentAmount($interestRate, $durationMonths);
        return $totalRepayment - $this->applied_amount;
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejected($query)
    {
        return $query->where('status', 'rejected');
    }

    public function scopeByMember($query, $memberId)
    {
        return $query->where('member_id', $memberId);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->whereHas('member', function ($q) use ($branchId) {
            $q->where('branch_id', $branchId);
        });
    }

    public function scopeRecentApplications($query, $days = 30)
    {
        return $query->where('applied_at', '>=', now()->subDays($days));
    }

    /**
     * Accessors
     */
    public function getStatusBadgeAttribute(): string
    {
        return match($this->status) {
            'pending' => '<span class="badge bg-warning">Pending</span>',
            'approved' => '<span class="badge bg-success">Approved</span>',
            'rejected' => '<span class="badge bg-danger">Rejected</span>',
            default => '<span class="badge bg-secondary">Unknown</span>'
        };
    }

    public function getFormattedAppliedAmountAttribute(): string
    {
        return '৳ ' . number_format($this->applied_amount, 2);
    }

    public function getFormattedAdvancePaymentAttribute(): string
    {
        return '৳ ' . number_format($this->advance_payment, 2);
    }

    public function getDaysFromApplicationAttribute(): int
    {
        return $this->applied_at ? $this->applied_at->diffInDays(now()) : 0;
    }

    /**
     * Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'member_id' => 'required|exists:members,id',
            'applied_amount' => 'required|numeric|min:1000|max:500000',
            'reason' => 'required|string|max:1000',
            'loan_cycle_number' => 'required|integer|min:1',
            'recommender' => 'nullable|string|max:100',
            'advance_payment' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($loanApplication) {
            if (!$loanApplication->applied_at) {
                $loanApplication->applied_at = now();
            }
        });
    }
}
