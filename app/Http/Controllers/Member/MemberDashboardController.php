<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Savings;
use App\Models\Transaction;

class MemberDashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        // Find member record for this user (you might need to link User and Member models)
        // For now, we'll assume the user email matches member email
        $member = Member::where('email', $user->email)->first();

        if (!$member) {
            return view('member.no-profile', compact('user'));
        }

        $stats = [
            'total_savings' => Savings::where('member_id', $member->id)->sum('balance'),
            'active_loans' => Loan::where('member_id', $member->id)->where('status', 'active')->count(),
            'total_loan_amount' => Loan::where('member_id', $member->id)->where('status', 'active')->sum('amount'),
            'monthly_installment' => Loan::where('member_id', $member->id)->where('status', 'active')->sum('monthly_installment'),
            'recent_transactions' => Transaction::where('member_id', $member->id)
                ->latest()
                ->take(10)
                ->get(),
            'savings_accounts' => Savings::where('member_id', $member->id)->get(),
            'loans' => Loan::where('member_id', $member->id)->latest()->get(),
        ];

        return view('member.dashboard', compact('stats', 'user', 'member'));
    }
}
